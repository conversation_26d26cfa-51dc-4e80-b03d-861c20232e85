<template>
  <main class="editor-container">
    <div class="editor-content">
      <div class="tiptap-container">
        <!-- Tiptap toolbar -->        <div id="editor-toolbar">
          <!-- Text formatting -->
          <span class="ql-formats">
            <button @click="editor?.chain().focus().toggleBold().run()" :class="{ 'is-active': editor?.isActive('bold') }" class="ql-bold" title="Bold"></button>
            <button @click="editor?.chain().focus().toggleItalic().run()" :class="{ 'is-active': editor?.isActive('italic') }" class="ql-italic" title="Italic"></button>
            <button @click="editor?.chain().focus().toggleUnderline().run()" :class="{ 'is-active': editor?.isActive('underline') }" class="ql-underline" title="Underline"></button>
            <button @click="editor?.chain().focus().toggleStrike().run()" :class="{ 'is-active': editor?.isActive('strike') }" class="ql-strike" title="Strikethrough"></button>
          </span>

          <!-- Text style -->
          <span class="ql-formats">
            <button @click="editor?.chain().focus().toggleHighlight().run()" :class="{ 'is-active': editor?.isActive('highlight') }" class="ql-highlight" title="Highlight"></button>
            <button @click="editor?.chain().focus().toggleCode().run()" :class="{ 'is-active': editor?.isActive('code') }" class="ql-code" title="Inline Code"></button>
            <button @click="editor?.chain().focus().toggleSuperscript().run()" :class="{ 'is-active': editor?.isActive('superscript') }" class="ql-superscript" title="Superscript"></button>
            <button @click="editor?.chain().focus().toggleSubscript().run()" :class="{ 'is-active': editor?.isActive('subscript') }" class="ql-subscript" title="Subscript"></button>
            <button @click="$emit('show-font-modal')" class="ql-font" title="Change font"></button>
            <button @click="$emit('show-color-modal')" class="ql-color" title="Text color"></button>
          </span>

          <!-- Headings -->
          <span class="ql-formats">
            <button @click="editor?.chain().focus().toggleHeading({ level: 1 }).run()" :class="{ 'is-active': editor?.isActive('heading', { level: 1 }) }" class="ql-header" value="1" title="Heading 1"></button>
            <button @click="editor?.chain().focus().toggleHeading({ level: 2 }).run()" :class="{ 'is-active': editor?.isActive('heading', { level: 2 }) }" class="ql-header" value="2" title="Heading 2"></button>
            <button @click="editor?.chain().focus().toggleHeading({ level: 3 }).run()" :class="{ 'is-active': editor?.isActive('heading', { level: 3 }) }" class="ql-header" value="3" title="Heading 3"></button>
          </span>

          <!-- Lists -->
          <span class="ql-formats">
            <button @click="editor?.chain().focus().toggleOrderedList().run()" :class="{ 'is-active': editor?.isActive('orderedList') }" class="ql-list" value="ordered" title="Numbered List"></button>
            <button @click="editor?.chain().focus().toggleBulletList().run()" :class="{ 'is-active': editor?.isActive('bulletList') }" class="ql-list" value="bullet" title="Bullet List"></button>
            <button @click="editor?.chain().focus().toggleTaskList().run()" :class="{ 'is-active': editor?.isActive('taskList') }" class="ql-checkbox" title="Task List"></button>
          </span>

          <!-- Block styles -->
          <span class="ql-formats">
            <button @click="editor?.chain().focus().toggleBlockquote().run()" :class="{ 'is-active': editor?.isActive('blockquote') }" class="ql-quote" title="Quote"></button>
            <button @click="editor?.chain().focus().setHorizontalRule().run()" class="ql-hr" title="Horizontal Line"></button>
            <button @click="editor?.chain().focus().toggleCodeBlock().run()" :class="{ 'is-active': editor?.isActive('codeBlock') }" class="ql-code-block" title="Code Block"></button>
          </span>

          <!-- Media and links -->
          <span class="ql-formats">
            <button @click="setLink" :class="{ 'is-active': editor?.isActive('link') }" class="ql-link" title="Add Link"></button>
            <button @click="addImage" class="ql-image" title="Add Image"></button>
          </span>

          <!-- History -->
          <span class="ql-formats">
            <button @click="editor?.chain().focus().undo().run()" class="ql-undo" title="Undo"></button>
            <button @click="editor?.chain().focus().redo().run()" class="ql-redo" title="Redo"></button>
          </span>
        </div>
        <div class="editor-area">
          <!-- Use the EditorContent component from @tiptap/vue-3 -->
          <editor-content v-if="editor" :editor="editor" class="tiptap-editor" spellcheck="false" />
        </div>
      </div>
    </div>    <!-- Image toolbar has been removed as requested -->

    <!-- Insert Link Modal -->
    <InsertLinkModal
      :visible="linkModalVisible"
      :mode="linkModalMode"
      :link-url="linkUrl"
      :link-text="linkText"
      :has-selection="hasSelection"
      @close="cancelLinkModal"
      @cancel="cancelLinkModal"
      @confirm="handleLinkConfirm"
    />



    <!-- Image Upload Modal -->
    <image-upload-modal
      :visible="imageModalVisible"
      @close="cancelImageModal"
      @confirm="handleImageConfirm"
      @image-selected="handleSelectedImage"
    />    <!-- Image Context Menu removed as requested -->

    <footer class="editor-footer">
      <div class="footer-divider"></div>
      <div class="footer-content">
        <div class="footer-left">
          <div class="stat-group">
            <span class="stat-label">Auto-saved</span>
          </div>
          <div v-if="lastSaveTime" class="stats-divider"></div>
          <div v-if="lastSaveTime" class="stat-group">
            <span class="stat-label">Last saved:</span>
            <span class="stat-value">{{ formattedLastSaveTime }}</span>
          </div>
        </div>
        <div class="footer-right">
          <div class="stat-group">
            <span class="stat-label">Words:</span>
            <span class="stat-value">{{ wordCount }}</span>
          </div>
          <div class="stats-divider"></div>
          <div class="stat-group">
            <span class="stat-label">Characters:</span>
            <span class="stat-value">{{ charCount }}</span>
          </div>
        </div>
      </div>
    </footer>
  </main>
</template>

<script lang="ts">
import { defineComponent, PropType, ref, computed, onMounted, onBeforeUnmount, watch } from 'vue';
import { useEditor, EditorContent } from '@tiptap/vue-3';
import { useEditorKeybinds } from '../../composables/useEditorKeybinds';
import StarterKit from '@tiptap/starter-kit';
import Link from '@tiptap/extension-link';
import TaskList from '@tiptap/extension-task-list';
import TaskItem from '@tiptap/extension-task-item';
import Placeholder from '@tiptap/extension-placeholder';
import Highlight from '@tiptap/extension-highlight';
import Table from '@tiptap/extension-table';
import TableRow from '@tiptap/extension-table-row';
import TableCell from '@tiptap/extension-table-cell';
import TableHeader from '@tiptap/extension-table-header';
import TextStyle from '@tiptap/extension-text-style';
import FontFamily from '@tiptap/extension-font-family';
import TextAlign from '@tiptap/extension-text-align';
import ResizeImage from 'tiptap-extension-resize-image';
import Color from '@tiptap/extension-color';
import Typography from '@tiptap/extension-typography';
import Underline from '@tiptap/extension-underline';
import Subscript from '@tiptap/extension-subscript';
import Superscript from '@tiptap/extension-superscript';
import { Extension } from '@tiptap/core';
import { Plugin, PluginKey } from '@tiptap/pm/state';
import type { Editor } from '@tiptap/core';
import type { EditorView } from '@tiptap/pm/view';
import type { Slice } from '@tiptap/pm/model';
import ImageUploadModal from '../modals/ImageUploadModal.vue';
import InsertLinkModal from '../modals/InsertLinkModal.vue';

interface Note {
  id?: number;
  title: string;
  content?: string;
  html_content?: string;
  folder_id?: number | null;
  created_at?: string;
  updated_at?: string;
}

export default defineComponent({
  name: 'NoteEditor',
  components: {
    EditorContent,
    ImageUploadModal,
    InsertLinkModal
  },
  props: {
    note: {
      type: Object as PropType<Note>,
      required: true
    },
    lastSaveTime: {
      type: String as PropType<string | null>,
      default: null
    }
  },
  emits: ['save', 'update', 'show-font-modal', 'show-color-modal'],
  setup(props, { emit }) {
    // Setup editor keybinds
    const { setupEditor, setupFunctions, activate: activateKeybinds, deactivate: deactivateKeybinds } = useEditorKeybinds();

    // State for font and color management
    const currentFont = ref('Montserrat');
    const currentColor = ref('#000000');

    // Helper function to download image from URL
    const downloadImageFromUrl = async (url: string): Promise<{ uint8Array: Uint8Array; fileName: string; mimeType: string }> => {
      try {
        const response = await fetch(url);
        if (!response.ok) {
          throw new Error(`Failed to fetch image: ${response.statusText}`);
        }

        const arrayBuffer = await response.arrayBuffer();
        const uint8Array = new Uint8Array(arrayBuffer);

        // Extract file extension from URL or content type
        const contentType = response.headers.get('content-type') || 'image/jpeg';
        const extension = contentType.split('/')[1] || 'jpg';
        const fileName = `pasted-image-${Date.now()}.${extension}`;

        return { uint8Array, fileName, mimeType: contentType };
      } catch (error) {
        console.error('Error downloading image:', error);
        throw error;
      }
    };

    // Helper function to process external image URLs
    const processExternalImageUrl = async (url: string): Promise<string> => {
      try {
        const { uint8Array, fileName, mimeType } = await downloadImageFromUrl(url);

        // Make sure we have a valid note ID
        const noteId = props.note.id ?? null;

        // Upload to server and get media entry
        const mediaFile = await window.db.media.save(
          noteId,
          Array.from(uint8Array), // Convert Uint8Array to regular array for IPC transfer
          fileName,
          mimeType
        );

        // Construct the image URL
        let imageUrl;
        if (window.db.media.getMediaUrl) {
          imageUrl = await window.db.media.getMediaUrl(mediaFile.file_path);
        } else {
          // Fallback to using the noti-media protocol directly
          imageUrl = `noti-media://${mediaFile.file_path.replace(/\\/g, '/')}`;
        }

        return imageUrl;
      } catch (error) {
        console.error('Error processing external image URL:', error);
        // Return original URL as fallback
        return url;
      }
    };

    // Create external image processing plugin
    const ExternalImageProcessor = Extension.create({
      name: 'externalImageProcessor',

      addProseMirrorPlugins() {
        return [
          new Plugin({
            key: new PluginKey('externalImageProcessor'),
            appendTransaction(transactions, _oldState, newState) {
              // Only process if there were actual changes
              if (!transactions.some(tr => tr.docChanged)) {
                return null;
              }

              const externalImages: { pos: number; node: any; src: string }[] = [];

              // Find all images with external URLs in the new document
              newState.doc.descendants((node, pos) => {
                if (node.type.name === 'image' && node.attrs.src) {
                  const src = node.attrs.src;
                  // Check if it's an external URL (not our local noti-media protocol)
                  if (src.startsWith('http://') || src.startsWith('https://')) {
                    externalImages.push({ pos, node, src });
                  }
                }
              });

              // If no external images found, return null
              if (externalImages.length === 0) {
                return null;
              }

              // Process external images asynchronously but more robustly
              // We need to handle this outside of the appendTransaction to avoid blocking
              setTimeout(() => {
                externalImages.forEach(({ pos, node, src }) => {
                  // Queue the processing for this image
                  processExternalImageUrl(src).then((localUrl) => {
                    if (localUrl !== src && editor.value) {
                      // Find the current position of this image in case the document changed
                      let currentPos = pos;
                      let foundImage = false;

                      editor.value.state.doc.descendants((currentNode, currentNodePos) => {
                        if (!foundImage &&
                            currentNode.type.name === 'image' &&
                            currentNode.attrs.src === src) {
                          currentPos = currentNodePos;
                          foundImage = true;
                          return false; // Stop iteration
                        }
                      });

                      if (foundImage) {
                        // Create a new transaction to replace the external URL
                        const updateTr = editor.value.state.tr.setNodeMarkup(currentPos, undefined, {
                          ...node.attrs,
                          src: localUrl
                        });
                        editor.value.view.dispatch(updateTr);
                      }
                    }
                  }).catch((error) => {
                    console.error(`Failed to process external image ${src}:`, error);
                  });
                });
              }, 0); // Use setTimeout with 0 delay to queue after current transaction

              // Return null since we're handling the updates asynchronously
              return null;
            }
          })
        ];
      }
    });

    // Function to create editor configuration
    const createEditorConfig = (content: string) => ({
      extensions: [
        StarterKit,
        TextStyle,
        Color,
        Typography,
        Underline,
        Subscript,
        Superscript,
        FontFamily.configure({
          types: ['textStyle']
        }),
        Link.configure({
          openOnClick: true,
          linkOnPaste: true,
          HTMLAttributes: {
            class: 'tiptap-link',
            rel: 'noopener noreferrer',
            target: '_blank',
          },
          validate: href => /^https?:\/\//.test(href) || href.startsWith('/') || href.startsWith('#'),
        }),
        TaskList,
        TaskItem.configure({
          nested: true,
        }),
        Placeholder.configure({
          placeholder: 'Write something...',
          emptyEditorClass: 'is-editor-empty',
        }),
        Highlight.configure({
          multicolor: true,
        }),
        Table.configure({
          resizable: true,
        }),
        TableRow,
        TableCell,
        TableHeader,
        TextAlign.configure({
          types: ['heading', 'paragraph', 'image'],
          defaultAlignment: 'left',
        }),
        // Using ResizeImage with enhanced constraints to prevent layout issues
        ResizeImage.configure({
          inline: false,
          allowBase64: false,
          HTMLAttributes: {
            class: 'tiptap-image',
            style: 'max-width: 100%; max-height: 80vh; object-fit: contain;',
          },
        }),
        // Add external image processor plugin
        ExternalImageProcessor,
      ],
      content: content,
      autofocus: true,
      editorProps: {
        attributes: {
          spellcheck: 'false'
        },
        handlePaste: (_view: EditorView, event: ClipboardEvent, _slice: Slice) => {
          // Check if clipboard contains image files
          const clipboardData = event.clipboardData;
          if (clipboardData && clipboardData.files.length > 0) {
            const file = clipboardData.files[0];
            if (file.type.startsWith('image/')) {
              // Handle clipboard image file
              processImageUpload(file, 'new');
              return true; // Prevent default paste handling
            }
          }

          // Let TipTap handle the paste normally
          // External images will be processed by the ExternalImageProcessor plugin
          return false;
        },
      },
      parseOptions: {
        preserveWhitespace: true,
      },
      onUpdate: ({ editor }: { editor: Editor }) => {
        emit('update', {
          html_content: editor.getHTML(),
          content: editor.getText()
        });
      },
    });

    // Initialize tiptap editor
    const editor = useEditor(createEditorConfig(props.note.html_content || '<p></p>'));

    // Watch for editor initialization and setup keybinds
    watch(editor, (newEditor) => {
      if (newEditor) {
        // Deactivate previous keybinds to prevent duplicate event listeners
        deactivateKeybinds();
        
        // Setup editor with keybinds
        setupEditor(newEditor);
        
        // Setup keybind functions
        setupFunctions({
          showLinkModal: () => {
            linkModalVisible.value = true;
            linkModalMode.value = 'new';
          },
          showFontModal: () => {
            emit('show-font-modal');
          },
          showColorModal: () => {
            emit('show-color-modal');
          },
          addImage
        });
        
        // Activate keybinds
        activateKeybinds();
      }
    }, { immediate: true });

    // Update editor content when note changes
    watch(() => props.note.id, (newNoteId, oldNoteId) => {
      if (newNoteId !== oldNoteId && editor.value) {
        editor.value.commands.setContent(props.note.html_content || '<p></p>');

        // Apply saved font to empty or new notes
        if (!props.note.html_content || props.note.html_content === '<p></p>') {
          setTimeout(() => {
            if (editor.value) {
              editor.value.commands.selectAll();
              editor.value.commands.setFontFamily(currentFont.value);
              editor.value.commands.setTextSelection(0);
            }
          }, 100);
        }
      }
    });

    // Also watch for external changes to html_content
    watch(() => props.note.html_content, (newContent) => {
      if (editor.value && newContent !== editor.value.getHTML()) {
        editor.value.commands.setContent(newContent || '<p></p>');
      }
    });

    // For word and character counts
    const wordCount = computed(() => {
      if (!editor.value) return 0;
      const text = editor.value.getText();
      return text ? text.trim().split(/\s+/).filter(Boolean).length : 0;
    });

    const charCount = computed(() => {
      if (!editor.value) return 0;
      return editor.value.getText().length;
    });

    // Format last save time for display
    const formattedLastSaveTime = computed(() => {
      if (!props.lastSaveTime) return '';

      const saveDate = new Date(props.lastSaveTime);
      const now = new Date();
      const diffInSeconds = Math.floor((now.getTime() - saveDate.getTime()) / 1000);

      if (diffInSeconds < 60) {
        return 'just now';
      } else if (diffInSeconds < 3600) {
        const minutes = Math.floor(diffInSeconds / 60);
        return `${minutes}m ago`;
      } else if (diffInSeconds < 86400) {
        const hours = Math.floor(diffInSeconds / 3600);
        return `${hours}h ago`;
      } else {
        return saveDate.toLocaleDateString();
      }
    });

    // Apply font family to editor selection
    const applyFontFamily = (fontFamily: string) => {
      console.log('applyFontFamily called with font:', fontFamily);

      if (!editor.value) {
        console.error('Editor not initialized');
        return;
      }

      try {
        // Update current font state
        currentFont.value = fontFamily;

        // Store the selected font as a user preference
        localStorage.setItem('noti-preferred-font', fontFamily);

        // Get the current selection range
        const { from } = editor.value.state.selection;

        // Select all text and apply the font
        editor.value.chain()
          .focus()
          .selectAll()
          .setFontFamily(fontFamily)
          .run();

        // Restore cursor position
        editor.value.chain().focus().setTextSelection(from).run();

        console.log('Font family applied successfully:', fontFamily);
      } catch (error) {
        console.error('Error applying font:', error);
      }
    };

    // Apply text color to editor selection
    const applyTextColor = (color: string) => {
      console.log('applyTextColor called with color:', color);

      if (!editor.value) {
        console.error('Editor not initialized');
        return;
      }

      try {
        // Update current color state
        currentColor.value = color;

        // Store the selected color as a user preference
        localStorage.setItem('noti-preferred-color', color);

        // Apply color to current selection or cursor position
        editor.value.chain().focus().setColor(color).run();

        console.log('Text color applied successfully:', color);
      } catch (error) {
        console.error('Error applying color:', error);
      }
    };

    // Remove text color from editor selection
    const removeTextColor = () => {
      console.log('removeTextColor called');

      if (!editor.value) {
        console.error('Editor not initialized');
        return;
      }

      try {
        // Reset current color state
        currentColor.value = '#000000';

        // Remove color from current selection
        editor.value.chain().focus().unsetColor().run();

        console.log('Text color removed successfully');
      } catch (error) {
        console.error('Error removing color:', error);
      }
    };

      // Image handling
    const imageModalVisible = ref(false);
    const selectedImage = ref<File | null>(null);
    const imageInsertMode = ref<'new' | 'replace'>('new');
    const imageReplacePosition = ref<number | null>(null);
    const imageReplaceNode = ref<any>(null);

    const addImage = () => {
      imageInsertMode.value = 'new';
      imageModalVisible.value = true;
      selectedImage.value = null;
    };

    const cancelImageModal = () => {
      imageModalVisible.value = false;
      selectedImage.value = null;
      imageReplacePosition.value = null;
      imageReplaceNode.value = null;
      imageInsertMode.value = 'new';
    };

    // Handle selected image from modal
    const handleSelectedImage = (file: File) => {
      selectedImage.value = file;
    };

    // Process the image upload and insertion
    const processImageUpload = async (file: File, mode: 'new' | 'replace' = 'new', posToReplace?: number, oldNode?: any) => {
      if (!editor.value) {
        console.error('Editor is not available');
        return;
      }

      // Make sure we have a valid note ID
      const noteId = props.note.id ?? null;

      try {
        // For browser environments, we need to use FileReader instead of Buffer
        const reader = new FileReader();

        reader.onload = async (event) => {
          if (!event.target?.result || !editor.value) return;

          // Convert the result to a Uint8Array
          const arrayBuffer = event.target.result as ArrayBuffer;
          const uint8Array = new Uint8Array(arrayBuffer);

          try {
            // Upload to server and get media entry
            const mediaFile = await window.db.media.save(
              noteId,
              Array.from(uint8Array), // Convert Uint8Array to regular array for IPC transfer
              file.name,
              file.type
            );              // Construct the image URL - either use getMediaUrl if available or construct it manually
              let imageUrl;
              if (window.db.media.getMediaUrl) {
                imageUrl = await window.db.media.getMediaUrl(mediaFile.file_path);
              } else {
                // Fallback to using the noti-media protocol directly
                imageUrl = `noti-media://${mediaFile.file_path.replace(/\\/g, '/')}`;
              }

              if (mode === 'replace' && posToReplace !== undefined && oldNode) {
                // Replace an existing image
                editor.value.chain()
                  .focus()
                  .deleteRange({ from: posToReplace, to: posToReplace + 1 })
                  .insertContentAt(posToReplace, {
                    type: 'image',
                    attrs: {
                      src: imageUrl,
                      alt: oldNode.attrs?.alt || file.name || 'Image'
                    }
                  })
                  .run();
              } else {
                // Insert new image at cursor position
                editor.value.chain().focus().setImage({ src: imageUrl, alt: file.name || 'Image' }).run();
              }

            // Close modal
            cancelImageModal();
          } catch (uploadError) {
            console.error('Error in upload process:', uploadError);
            alert('Failed to upload image. Please try again.');
          }
        };

        reader.onerror = (error) => {
          console.error('Error reading file:', error);
          alert('Failed to read image file. Please try again.');
        };

        // Start reading the file
        reader.readAsArrayBuffer(file);
      } catch (error) {
        console.error('Error in image upload process:', error);
        alert('Failed to process image. Please try again.');
      }
    };

    // Handle image upload on confirm
    const handleImageConfirm = async (file: File) => {
      if (imageInsertMode.value === 'replace' && imageReplacePosition.value !== null && imageReplaceNode.value) {
        // Replace mode
        await processImageUpload(file, 'replace', imageReplacePosition.value, imageReplaceNode.value);
      } else {
        // Normal insertion mode
        await processImageUpload(file, 'new');
      }
    };

    // Load user's preferred font and color on mount
    onMounted(() => {
      try {
        const savedFont = localStorage.getItem('noti-preferred-font');
        if (savedFont) {
          currentFont.value = savedFont;
        }

        const savedColor = localStorage.getItem('noti-preferred-color');
        if (savedColor) {
          currentColor.value = savedColor;
        }

        // Apply the preferred font when the editor is ready
        if (editor.value) {          // Wait for editor to fully initialize
          setTimeout(() => {
            if (editor.value) {
              try {
                // For new documents, apply font to entire content
                if (!props.note.html_content || props.note.html_content === '<p></p>') {
                  editor.value.commands.selectAll();
                  editor.value.commands.setFontFamily(currentFont.value);
                  // Safer cursor positioning - move to start of document
                  editor.value.commands.focus('start');
                }
                // For existing documents without explicit font, apply the preferred font
                else if (props.note.html_content && !props.note.html_content.includes('font-family')) {
                  editor.value.commands.selectAll();
                  editor.value.commands.setFontFamily(currentFont.value);
                  // Safer cursor positioning - move to start of document
                  editor.value.commands.focus('start');
                }
              } catch (error) {
                console.error('Error applying font or setting cursor:', error);
              }
            }
          }, 300); // Increased timeout to ensure editor is ready
        }
          // Set up drag and drop for images
        const editorElement = document.querySelector('.tiptap-editor');
        if (editorElement) {
          const handleDragOver = (e: Event) => {
            // Prevent default to allow drop
            e.preventDefault();
          };

          const handleDrop = (e: Event) => {
            e.preventDefault();

            if (!editor.value) return;

            // Handle file drops
            const dragEvent = e as DragEvent;
            if (dragEvent.dataTransfer?.files && dragEvent.dataTransfer.files.length > 0) {
              const file = dragEvent.dataTransfer.files[0];

              // Check if it's an image
              if (file.type.startsWith('image/')) {
                // Set the file and open image modal
                selectedImage.value = file;
                imageInsertMode.value = 'new';
                imageModalVisible.value = true;
              }
            }
          };

          editorElement.addEventListener('dragover', handleDragOver);
          editorElement.addEventListener('drop', handleDrop);

          // Clean up on unmount
          onBeforeUnmount(() => {
            editorElement.removeEventListener('dragover', handleDragOver);
            editorElement.removeEventListener('drop', handleDrop);
          });
        }
      } catch (error) {
        console.error('Error loading font preference:', error);
      }
    });

    // Enhanced link handling
    const linkModalVisible = ref(false);
    const linkModalMode = ref<'edit' | 'new'>('new');
    const linkUrl = ref('');
    const linkText = ref('');
    const hasSelection = ref(false);
    const urlInput = ref<HTMLInputElement | null>(null);

    const setLink = () => {
      if (!editor.value) return;

      const { from, to } = editor.value.state.selection;
      hasSelection.value = from !== to;

      // If there's no selection and there's an active link, edit the current link
      if (from === to && editor.value.isActive('link')) {
        linkModalMode.value = 'edit';
        const previousUrl = editor.value.getAttributes('link').href || '';
        linkUrl.value = previousUrl;
        linkModalVisible.value = true;

        // Focus the URL input field when modal appears
        setTimeout(() => {
          urlInput.value?.focus();
        }, 100);
        return;
      }

      // If there's no selection and no active link, prompt user to enter link text and URL
      if (from === to && !editor.value.isActive('link')) {
        linkModalMode.value = 'new';
        linkText.value = '';
        linkUrl.value = 'https://';
        linkModalVisible.value = true;

        // Focus the link text input field when modal appears
        setTimeout(() => {
          const linkTextInput = document.getElementById('linkText');
          if (linkTextInput) linkTextInput.focus();
        }, 100);
        return;
      }

      // If there's a selection, add link to the selected text
      linkModalMode.value = 'new';
      const previousUrl = editor.value.getAttributes('link').href || '';
      linkUrl.value = previousUrl || 'https://';
      linkModalVisible.value = true;

      // Focus the URL input field when modal appears
      setTimeout(() => {
        urlInput.value?.focus();
      }, 100);
    };



    const cancelLinkModal = () => {
      linkModalVisible.value = false;
      linkUrl.value = '';
      linkText.value = '';
    };

    const handleLinkConfirm = (linkData: { url: string; text: string }) => {
      if (!editor.value) return;
      const { from, to } = editor.value.state.selection;

      // Update local values with the data from the modal
      linkUrl.value = linkData.url;
      linkText.value = linkData.text;

      // Handle edit mode (updating existing link)
      if (linkModalMode.value === 'edit') {
        if (linkData.url === '') {
          // Remove the link if URL is empty
          editor.value.chain().focus().extendMarkRange('link').unsetLink().run();
        } else {
          // Update the link URL
          editor.value.chain()
            .focus()
            .extendMarkRange('link')
            .setLink({
              href: linkData.url,
              target: '_blank'
            })
            .run();
        }
      }
      // Handle new link with no selection (create new linked text)
      else if (from === to && !hasSelection.value) {
        if (linkData.url && linkData.text) {
          editor.value.chain()
            .focus()
            .insertContent(`<a href="${linkData.url}" class="tiptap-link" target="_blank" rel="noopener noreferrer">${linkData.text}</a>`)
            .run();
        }
      }
      // Handle new link with selection (apply link to selected text)
      else {
        if (linkData.url === '') {
          // Remove the link if URL is empty
          editor.value.chain().focus().extendMarkRange('link').unsetLink().run();
        } else {
          // Set the link with proper attributes
          editor.value.chain()
            .focus()
            .extendMarkRange('link')
            .setLink({
              href: linkData.url,
              target: '_blank'
            })
            .run();
        }
      }

      // Reset and close the modal
      linkModalVisible.value = false;
      linkUrl.value = '';
      linkText.value = '';
    };

    // Cleanup on component unmount
    onBeforeUnmount(() => {
      // Deactivate keybinds
      deactivateKeybinds();
      
      // Destroy editor
      editor.value?.destroy();
    });    return {
      editor,
      wordCount,
      charCount,
      formattedLastSaveTime,
      setLink,
      linkModalVisible,
      linkModalMode,
      linkUrl,
      linkText,
      hasSelection,
      handleLinkConfirm,
      cancelLinkModal,
      urlInput,
      currentFont,
      currentColor,
      applyFontFamily,
      applyTextColor,
      removeTextColor,
      imageModalVisible,
      selectedImage,
      addImage,
      cancelImageModal,
      handleSelectedImage,
      handleImageConfirm,
    };
  }
});
</script>

<style scoped>
.editor-container {
  font-family:
    Montserrat,
    -apple-system,
    Roboto,
    Helvetica,
    sans-serif;
  font-weight: 500;
  display: flex;
  flex-direction: column;
  height: 100%;
  max-width: 100%; /* Prevent container overflow */
  position: relative;
  overflow: hidden;
  /* Enable text selection inside the editor */
  user-select: text;
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
}

.editor-content {
  flex: 1;
  overflow: auto;
  max-width: 100%; /* Prevent content overflow */
  background-color: var(--color-bg-secondary);
  border-radius: 12px;
  margin: 10px;
  margin-bottom: 50px;
  min-height: 300px;
  display: flex;
  flex-direction: column;
  box-shadow: none;
}

.tiptap-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  max-width: 100%; /* Prevent container overflow */
  background-color: var(--color-card-bg);
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid var(--color-card-border);
}

/* Editor area styles - Enhanced overflow protection */
.editor-area {
  flex: 1;
  overflow: auto;
  max-width: 100%; /* Prevent area overflow */
  min-height: 300px;
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  line-height: 1.6;
  color: var(--color-text-primary);
  display: flex;
  flex-direction: column;
  position: relative;
  scrollbar-width: thin;
  scrollbar-color: var(--color-scrollbar-thumb) var(--color-scrollbar-track);
}

.editor-area::-webkit-scrollbar {
  width: 8px;
}

.editor-area::-webkit-scrollbar-track {
  background: var(--color-scrollbar-track);
  border-radius: 8px;
}

.editor-area::-webkit-scrollbar-thumb {
  background-color: #d1d1d1;
  border-radius: 8px;
  border: 2px solid #f5f5f5;
}

.editor-area::-webkit-scrollbar-thumb:hover {
  background-color: #c1c1c1;
}

.tiptap-editor {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Editor toolbar styles */
#editor-toolbar {
  margin-bottom: 20px;
  border: 1px solid var(--color-border-primary);
  border-radius: 10px;
  padding: 10px;
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  background-color: var(--color-card-bg);
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.ql-formats {
  display: flex;
  gap: 4px;
}

.ql-formats button {
  width: 32px;
  height: 32px;
  background-color: var(--color-input-bg);
  border: 1px solid var(--color-border-primary);
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  transition: all 0.15s ease;
  background-repeat: no-repeat;
  background-position: center center;
  background-size: 18px;
}

.ql-formats button:hover {
  background-color: var(--color-nav-item-hover);
}

.ql-formats button.is-active {
  background-color: var(--color-nav-item-active);
  border-color: var(--color-border-hover);
}

/* Reset any previous background images */
.ql-formats button {
  background-image: none !important;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

/* Fallback text for accessibility in case icons fail to load */
.ql-bold::after { content: 'B'; font-weight: bold; opacity: 0; position: absolute; }
.ql-italic::after { content: 'I'; font-style: italic; opacity: 0; position: absolute; }
.ql-underline::after { content: 'U'; text-decoration: underline; opacity: 0; position: absolute; }
.ql-strike::after { content: 'S'; text-decoration: line-through; opacity: 0; position: absolute; }
.ql-code::after { content: '</>'; font-family: monospace; opacity: 0; position: absolute; }
.ql-highlight::after { content: 'H'; opacity: 0; position: absolute; }
.ql-header[value="1"]::after { content: 'H1'; opacity: 0; position: absolute; }
.ql-header[value="2"]::after { content: 'H2'; opacity: 0; position: absolute; }
.ql-header[value="3"]::after { content: 'H3'; opacity: 0; position: absolute; }

/* Show fallback text if icon fails to load */
.ql-formats button::before:not([style*="background-image"]) + ::after {
  opacity: 1;
}

/* Style each button separately */
.ql-formats button.ql-bold::before {
  content: '';
  width: 18px;
  height: 18px;
  background: url('/icons/bold-icon.svg') no-repeat center center;
  background-size: contain;
}

.ql-formats button.ql-italic::before {
  content: '';
  width: 18px;
  height: 18px;
  background: url('/icons/italic-icon.svg') no-repeat center center;
  background-size: contain;
}

.ql-formats button.ql-underline::before {
  content: '';
  width: 18px;
  height: 18px;
  background: url('/icons/underline-icon.svg') no-repeat center center;
  background-size: contain;
}

.ql-formats button.ql-strike::before {
  content: '';
  width: 18px;
  height: 18px;
  background: url('/icons/strike-icon.svg') no-repeat center center;
  background-size: contain;
}

.ql-formats button.ql-highlight::before {
  content: '';
  width: 18px;
  height: 18px;
  background: url('/icons/highlight-icon.svg') no-repeat center center;
  background-size: contain;
}

.ql-formats button.ql-code::before {
  content: '';
  width: 18px;
  height: 18px;
  background: url('/icons/code-icon.svg') no-repeat center center;
  background-size: contain;
}

.ql-formats button.ql-list[value="ordered"]::before {
  content: '';
  width: 18px;
  height: 18px;
  background: url('/icons/list-ordered-icon.svg') no-repeat center center;
  background-size: contain;
}

.ql-formats button.ql-list[value="bullet"]::before {
  content: '';
  width: 18px;
  height: 18px;
  background: url('/icons/list-bullet-icon.svg') no-repeat center center;
  background-size: contain;
}

.ql-formats button.ql-link::before {
  content: '';
  width: 18px;
  height: 18px;
  background: url('/icons/link-icon.svg') no-repeat center center;
  background-size: contain;
}

.ql-formats button.ql-checkbox::before {
  content: '';
  width: 18px;
  height: 18px;
  background: url('/icons/checkbox-icon.svg') no-repeat center center;
  background-size: contain;
}

.ql-formats button.ql-quote::before {
  content: '';
  width: 18px;
  height: 18px;
  background: url('/icons/quotes-icon.svg') no-repeat center center;
  background-size: contain;
}

.ql-formats button.ql-hr::before {
  content: '';
  width: 18px;
  height: 18px;
  background: url('/icons/hr-icon.svg') no-repeat center center;
  background-size: contain;
}

.ql-formats button.ql-code-block::before {
  content: '';
  width: 18px;
  height: 18px;
  background: url('/icons/code-block-icon.svg') no-repeat center center;
  background-size: contain;
}

.ql-formats button.ql-font::before {
  content: '';
  width: 18px;
  height: 18px;
  background: url('/icons/font-icon.svg') no-repeat center center;
  background-size: contain;
}

.ql-formats button.ql-color::before {
  content: '';
  width: 18px;
  height: 18px;
  background: url('/icons/color-icon.svg') no-repeat center center;
  background-size: contain;
}

.ql-formats button.ql-image::before {
  content: '';
  width: 18px;
  height: 18px;
  background: url('/icons/image-icon.svg') no-repeat center center;
  background-size: contain;
}

/* Hide other icon styles that might be causing conflicts */
button.ql-bold {
  background-image: none !important;
}
button.ql-italic {
  background-image: none !important;
}
button.ql-underline {
  background-image: none !important;
}
button.ql-strike {
  background-image: none !important;
}
button.ql-highlight {
  background-image: none !important;
}
button.ql-code {
  background-image: none !important;
}
button.ql-list[value="ordered"] {
  background-image: none !important;
}
button.ql-list[value="bullet"] {
  background-image: none !important;
}
button.ql-link {
  background-image: none !important;
}
button.ql-checkbox {
  background-image: none !important;
}
button.ql-quote {
  background-image: none !important;
}
button.ql-hr {
  background-image: none !important;
}
button.ql-code-block {
  background-image: none !important;
}
button.ql-font {
  background-image: none !important;
}
button.ql-color {
  background-image: none !important;
}
button.ql-image {
  background-image: none !important;
}

/* Button icons */
button.ql-bold {
  background-image: url('/icons/bold-icon.svg');
}
button.ql-italic {
  background-image: url('/icons/italic-icon.svg');
}
button.ql-underline {
  background-image: url('/icons/underline-icon.svg');
}
button.ql-strike {
  background-image: url('/icons/strike-icon.svg');
}
button.ql-highlight {
  background-image: url('/icons/highlight-icon.svg');
}
button.ql-code {
  background-image: url('/icons/code-icon.svg');
}
.ql-formats button.ql-header[value="1"]::before {
  content: '';
  width: 18px;
  height: 18px;
  background: url('/icons/header1-icon.svg') no-repeat center center;
  background-size: contain;
}

.ql-formats button.ql-header[value="2"]::before {
  content: '';
  width: 18px;
  height: 18px;
  background: url('/icons/header2-icon.svg') no-repeat center center;
  background-size: contain;
}

.ql-formats button.ql-header[value="3"]::before {
  content: '';
  width: 18px;
  height: 18px;
  background: url('/icons/header3-icon.svg') no-repeat center center;
  background-size: contain;
}
button.ql-list[value="ordered"] {
  background-image: url('/icons/list-ordered-icon.svg');
}
button.ql-list[value="bullet"] {
  background-image: url('/icons/list-bullet-icon.svg');
}
button.ql-link {
  background-image: url('/icons/link-icon.svg');
}
button.ql-checkbox {
  background-image: url('/icons/checkbox-icon.svg');
}
button.ql-quote {
  background-image: url('/icons/quotes-icon.svg');
}
button.ql-hr {
  background-image: url('/icons/hr-icon.svg');
}
button.ql-code-block {
  background-image: url('/icons/code-block-icon.svg');
}
.ql-formats button.ql-superscript::before {
  content: '';
  width: 18px;
  height: 18px;
  background: url('/icons/superscript-icon.svg') no-repeat center center;
  background-size: contain;
}

.ql-formats button.ql-subscript::before {
  content: '';
  width: 18px;
  height: 18px;
  background: url('/icons/subscript-icon.svg') no-repeat center center;
  background-size: contain;
}
.ql-formats button.ql-undo::before {
  content: '';
  width: 18px;
  height: 18px;
  background: url('/icons/undo-icon.svg') no-repeat center center;
  background-size: contain;
}
.ql-formats button.ql-redo::before {
  content: '';
  width: 18px;
  height: 18px;
  background: url('/icons/redo-icon.svg') no-repeat center center;
  background-size: contain;
}
button.ql-font {
  background-image: url('/icons/font-icon.svg');
}
button.ql-color {
  background-image: url('/icons/color-icon.svg');
}
button.ql-image {
  background-image: url('/icons/image-icon.svg');
}

/* Tiptap content styles - Enhanced overflow handling */
:deep(.ProseMirror) {
  outline: none;
  min-height: 200px;
  height: 100%;
  padding: 1.5rem;
  box-sizing: border-box;
  width: 100%;
  max-width: 100%; /* Prevent horizontal overflow */
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden; /* Prevent horizontal scrolling */
  word-wrap: break-word; /* Handle long content */
  scrollbar-width: thin;
  scrollbar-color: #d1d1d1 #f5f5f5;
}

:deep(.ProseMirror)::-webkit-scrollbar {
  width: 8px;
}

:deep(.ProseMirror)::-webkit-scrollbar-track {
  background: #f5f5f5;
  border-radius: 8px;
}

:deep(.ProseMirror)::-webkit-scrollbar-thumb {
  background-color: #d1d1d1;
  border-radius: 8px;
  border: 2px solid #f5f5f5;
}

:deep(.ProseMirror)::-webkit-scrollbar-thumb:hover {
  background-color: #c1c1c1;
}

:deep(.ProseMirror) > * + * {
  margin-top: 0.75em;
}

:deep(.ProseMirror p) {
  margin: 0;
}

:deep(.ProseMirror h1) {
  font-size: 1.75em;
  margin-top: 0.67em;
  margin-bottom: 0.67em;
  font-weight: 700;
}

:deep(.ProseMirror h2) {
  font-size: 1.5em;
  margin-top: 0.83em;
  margin-bottom: 0.83em;
  font-weight: 600;
}

:deep(.ProseMirror h3) {
  font-size: 1.25em;
  margin-top: 1em;
  margin-bottom: 1em;
  font-weight: 500;
}

:deep(.ProseMirror ul),
:deep(.ProseMirror ol) {
  padding: 0 1rem;
}

:deep(.ProseMirror a) {
  color: #1e88e5;
  text-decoration: underline;
}

:deep(.tiptap-link) {
  color: #1e88e5;
  text-decoration: underline;
  cursor: pointer;
  transition: color 0.2s;
  position: relative;
}

:deep(.tiptap-link:hover) {
  color: #0d47a1;
}

:deep(.ProseMirror blockquote) {
  border-left: 3px solid #e0e0e0;
  padding-left: 1rem;
  font-style: italic;
  color: #666;
  margin: 1rem 0;
}

:deep(.ProseMirror hr) {
  border: none;
  border-top: 2px solid #e0e0e0;
  margin: 2rem 0;
}

:deep(.ProseMirror pre) {
  background-color: var(--color-code-block-bg);
  color: var(--color-code-text);
  border-radius: 6px;
  padding: 1rem;
  font-family: monospace;
  overflow-x: auto;
}

:deep(.ProseMirror code) {
  background-color: var(--color-code-bg);
  color: var(--color-code-text);
  border-radius: 3px;
  padding: 0.2em 0.4em;
  font-family: monospace;
  font-size: 0.9em;
}

:deep(.ProseMirror ul[data-type="taskList"]) {
  list-style-type: none;
  padding-left: 0;
}

:deep(.ProseMirror ul[data-type="taskList"] li) {
  display: flex;
  align-items: flex-start;
  margin-bottom: 0.5em;
}

:deep(.ProseMirror ul[data-type="taskList"] li > label) {
  margin-right: 0.5em;
  user-select: none;
}

:deep(.ProseMirror ul[data-type="taskList"] li > div) {
  flex: 1;
}

:deep(.ProseMirror mark) {
  background-color: #ffed9c;
  border-radius: 2px;
}

/* Allow font-family to be properly applied */
:deep([style*="font-family:"]) {
  font-family: inherit !important;
}

/* Support for specific fonts */
:deep([style*="font-family: Montserrat"]) {
  font-family: 'Montserrat', sans-serif !important;
}

:deep([style*="font-family: Arial"]) {
  font-family: 'Arial', sans-serif !important;
}

:deep([style*="font-family: Times New Roman"]) {
  font-family: 'Times New Roman', serif !important;
}

:deep([style*="font-family: Georgia"]) {
  font-family: 'Georgia', serif !important;
}

:deep([style*="font-family: Courier New"]) {
  font-family: 'Courier New', monospace !important;
}

:deep([style*="font-family: Verdana"]) {
  font-family: 'Verdana', sans-serif !important;
}

:deep([style*="font-family: Helvetica"]) {
  font-family: 'Helvetica', sans-serif !important;
}

:deep([style*="font-family: Roboto"]) {
  font-family: 'Roboto', sans-serif !important;
}

:deep([style*="font-family: Open Sans"]) {
  font-family: 'Open Sans', sans-serif !important;
}

:deep([style*="font-family: Lato"]) {
  font-family: 'Lato', sans-serif !important;
}

:deep(.ProseMirror table) {
  border-collapse: collapse;
  table-layout: fixed;
  width: 100%;
  margin: 1rem 0;
  overflow: hidden;
}

:deep(.ProseMirror th),
:deep(.ProseMirror td) {
  border: 1px solid #e0e0e0;
  padding: 0.5rem;
  vertical-align: top;
  position: relative;
}

:deep(.ProseMirror th) {
  font-weight: bold;
  background-color: #f9f9f9;
}

/* Footer styles */
.editor-footer {
  background-color: var(--color-bg-primary);
  display: flex;
  width: 100%;
  padding-bottom: 10px;
  flex-direction: column;
  align-items: stretch;
  font-weight: 400;
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 10;
  min-height: 40px;
  box-shadow: none;
}

.footer-divider {
  background-color: var(--color-border-primary);
  height: 1px;
  width: 100%;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
  padding: 0 20px;
}

.footer-left {
  display: flex;
  align-items: center;
  gap: 15px;
}

.footer-right {
  display: flex;
  align-items: center;
  gap: 15px;
}

.stat-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.stat-label {
  color: var(--color-text-secondary);
  font-size: 13px;
}

.stat-value {
  color: var(--color-text-primary);
  font-size: 13px;
  text-align: right;
}

.stats-divider {
  background-color: var(--color-border-secondary);
  width: 1px;
  height: 18px;
}



/* Image styles - Enhanced containment to prevent layout issues */
:deep(.ProseMirror img) {
  max-width: 100% !important;
  width: auto !important;
  height: auto !important;
  max-height: 80vh; /* Prevent images from being taller than viewport */
  margin: 1em 0;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  object-fit: contain; /* Ensure proper scaling */
  display: block; /* Prevent inline layout issues */
}

:deep(.ProseMirror .tiptap-image) {
  display: block !important;
  max-width: 100% !important;
  width: auto !important;
  margin: 1em auto;
  height: auto !important; /* Maintain aspect ratio when resizing */
  max-height: 80vh !important; /* Prevent images from being taller than viewport */
  object-fit: contain !important; /* Ensure proper scaling */
}

/* Image alignment styles */
:deep(.ProseMirror .is-text-align-left img) {
  margin-left: 0;
  margin-right: auto;
}

:deep(.ProseMirror .is-text-align-center img) {
  margin-left: auto;
  margin-right: auto;
}

:deep(.ProseMirror .is-text-align-right img) {
  margin-left: auto;
  margin-right: 0;
}

/* Support for resizable images - Enhanced constraints */
:deep(.ProseMirror img.resize-cursor) {
  cursor: nwse-resize;
  max-width: 100% !important; /* Maintain constraints during resize */
  max-height: 80vh !important; /* Maintain height constraints during resize */
}

:deep(.ProseMirror img.ProseMirror-selectednode) {
  outline: 3px solid #68CEF8;
  max-width: 100% !important; /* Maintain constraints when selected */
  max-height: 80vh !important; /* Maintain height constraints when selected */
}

/* Additional safety rules for image containment */
:deep(.ProseMirror img[style*="width"]) {
  max-width: 100% !important; /* Override any inline width styles that exceed container */
}

:deep(.ProseMirror img[style*="height"]) {
  max-height: 80vh !important; /* Override any inline height styles that exceed viewport */
}

/* Ensure images don't break out of their containers during any state */
:deep(.ProseMirror img:not([style*="max-width"])) {
  max-width: 100% !important;
}

:deep(.ProseMirror img:not([style*="max-height"])) {
  max-height: 80vh !important;
}

/* Force containment for all images regardless of inline styles */
:deep(.ProseMirror img) {
  box-sizing: border-box !important;
  contain: layout style !important; /* CSS containment to prevent layout overflow */
}

/* Ensure the ProseMirror container itself has proper containment */
:deep(.ProseMirror) {
  contain: layout style !important; /* Prevent content from affecting parent layout */
}

/* Image context menu styles */
.image-context-menu {
  position: fixed;
  z-index: 10000; /* Increased z-index to ensure it's above other elements */
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  padding: 8px 0;
  min-width: 200px;
  max-width: 280px;
  user-select: none;
  animation: fade-in 0.15s ease;
  border: 1px solid #e0e0e0;
  font-family: 'Montserrat', sans-serif;
  pointer-events: auto; /* Ensure it can receive mouse events */
}

@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(-5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.context-menu-section {
  padding: 4px 0;
}

.context-menu-heading {
  font-size: 12px;
  font-weight: 600;
  color: #777;
  padding: 6px 16px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.context-menu-item {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  cursor: pointer;
  color: #333;
  font-size: 14px;
  transition: all 0.1s ease;
}

.context-menu-item:hover {
  background-color: #f5f7fa;
  color: #1e88e5;
}

.context-icon {
  margin-right: 10px;
  font-size: 16px;
  width: 18px;
  text-align: center;
}

.context-menu-divider {
  height: 1px;
  background-color: #e0e0e0;
  margin: 4px 0;
}

.context-menu-alignment {
  display: flex;
  justify-content: space-around;
  padding: 8px 16px;
}

.context-menu-align-btn {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  cursor: pointer;
  background-color: #f0f0f0;
  font-size: 16px;
  transition: all 0.15s ease;
}

.context-menu-align-btn:hover {
  background-color: #e5e5e5;
}

.context-menu-align-btn.active {
  background-color: #1e88e5;
  color: white;
}

@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(-5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Image toolbar styles */
.image-toolbar {
  position: fixed;
  z-index: 100;
  background-color: white;
  border-radius: 6px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  padding: 6px;
  gap: 8px;
  animation: fade-in 0.2s ease;
  top: 12px;
  left: 50%;
  transform: translateX(-50%);
}

.image-toolbar-section {
  display: flex;
  gap: 4px;
}

.image-toolbar-divider {
  width: 1px;
  height: 24px;
  background-color: #e3e3e3;
}

.toolbar-button {
  width: 32px;
  height: 32px;
  border-radius: 4px;
  border: 1px solid #e3e3e3;
  background-color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.15s ease;
}

.toolbar-button:hover {
  background-color: #f5f5f5;
}

.toolbar-button.is-active {
  background-color: #e9e9e9;
  border-color: #d9d9d9;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05);
}

.toolbar-icon {
  font-size: 16px;
  color: var(--color-text-secondary);
}
</style>
